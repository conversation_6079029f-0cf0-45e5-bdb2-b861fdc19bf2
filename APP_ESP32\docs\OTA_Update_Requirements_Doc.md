# BLE OTA Update System Requirements Document 

####  Platform Support
- **ESP32 Variants:**  ESP32-S3
- **ESP-IDF Version:** 5.4.1
- **Python Version:** 3.8+
- **BLE Stack:** NimBLE


####  Python Client Components
- **nimble_ota_client.py:** Main OTA client implementation
- **Ota_flash_firmware_select.bat:** Batch file interface
- **Device discovery and connection management**
- **Progress tracking and error handling**

## Installation and Dependencies

#### ESP32 Build and Flash
```bash
# Navigate to project directory
cd APP_ESP32

# Configure project
idf.py menuconfig

# Build project
idf.py build

# Flash firmware
idf.py -p COM3 flash monitor  # Windows
idf.py -p /dev/ttyUSB0 flash monitor  # Linux
```

### Python Client Environment Setup

#### Python Installation
**Requirement:** Python 3.8 or higher

**Windows:**
```bash
# Download from python.org or use Microsoft Store
# Verify installation
python --version
pip --version
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip

# Verify installation
python3 --version
pip3 --version
```

**macOS:**
```bash
# Using Homebrew
brew install python3

# Verify installation
python3 --version
pip3 --version
```

#### Python Dependencies Installation

# Install required packages
pip install bleak>=0.22.3
pip install bleak-winrt>=1.2.0    # Windows-specific BLE backend
pip install pyserial>=3.5         # For serial communication
pip install crcmod>=1.7           # For CRC calculations
```

**Complete requirements.txt:**
```txt
bleak>=0.22.3
bleak-winrt>=1.2.0
asyncio>=3.4.3
pyserial>=3.5
crcmod>=1.7
colorama>=0.4.4
click>=8.0.0
```

**Install from requirements file:**
```bash
pip install -r requirements.txt
```


#### End-to-End Test
```bash
# Run OTA client
cd APP_ESP32
python nimble_ota_client.py

# Or use batch file
Ota_flash_firmware_select.bat
```


**Debug Steps:**
```bash
# Enable verbose logging
python nimble_ota_client.py --verbose

# Check ESP32 logs
idf.py -p COM3 monitor

# Look for:
# - "BLE CONNECTION ESTABLISHED"
# - "subscribe event" logs
# - "OTA service initialized successfully"
```

#### Installation Commands for Current Versions

**Windows (Recommended):**
```bash
pip install bleak==0.22.3 bleak-winrt==1.2.0
```

**Linux/macOS:**
```bash
pip install bleak==0.22.3
```



