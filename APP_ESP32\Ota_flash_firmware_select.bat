@echo off
REM ============================================================================
REM Universal Firmware Flash Script with Device Number Selection
REM ============================================================================
REM This script lets you select devices by pressing a number!
REM No more typing long MAC addresses!
REM ============================================================================

setlocal enabledelayedexpansion

REM Configuration
set BUILD_DIR=build
set PYTHON_SCRIPT=nimble_ota_client.py

echo ============================================================================
echo                    UNIVERSAL FIRMWARE FLASH TOOL
echo                   Select Devices by Number - Easy Mode!
echo ============================================================================
echo.

REM Check if build directory exists
if not exist "%BUILD_DIR%" (
    echo ERROR: Build directory not found: %BUILD_DIR%
    echo Please build the firmware first using: idf.py build
    pause
    exit /b 1
)

REM Check for firmware files
echo Checking for firmware files in %BUILD_DIR%...
set FIRMWARE_COUNT=0

for %%f in ("%BUILD_DIR%\*.bin") do (
    if exist "%%f" (
        set /a FIRMWARE_COUNT+=1
        echo   Found: %%~nxf
    )
)

if %FIRMWARE_COUNT% EQU 0 (
    echo ERROR: No firmware files found in %BUILD_DIR%
    echo Please build the firmware first using: idf.py build
    pause
    exit /b 1
)

echo Found %FIRMWARE_COUNT% firmware file(s) ready for selection
echo.

REM Check if Python script exists
if not exist "%PYTHON_SCRIPT%" (
    echo ERROR: Python script not found: %PYTHON_SCRIPT%
    pause
    exit /b 1
)

REM Step 1: Device Selection and Scanning
echo ============================================================================
echo                           STEP 1: DEVICE SELECTION
echo ============================================================================
echo Scanning for MR/CT/COP/ESP32 devices...
echo This will show you a numbered list of available devices.
echo You can then select which device to update by entering its number.
echo ============================================================================
echo.

REM Use Python script to scan and get device list
echo Scanning for devices...
python "%PYTHON_SCRIPT%" --scan > device_scan.tmp 2>&1

REM Check if scan was successful
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Device scan failed
    if exist device_scan.tmp type device_scan.tmp
    if exist device_scan.tmp del device_scan.tmp
    pause
    exit /b 1
)

REM Parse scan results and create device menu
set DEVICE_COUNT=0
if exist device_scan.tmp (
    echo Available devices:
    echo.

    REM Read scan results and extract device info
    for /f "tokens=*" %%a in ('findstr /C:"Found target device:" device_scan.tmp') do (
        set /a DEVICE_COUNT+=1
        set LINE=%%a
        REM Extract device name and MAC from "Found target device: NAME (MAC)"
        for /f "tokens=4,5 delims=: ()" %%b in ("!LINE!") do (
            set DEVICE_!DEVICE_COUNT!_NAME=%%b
            set DEVICE_!DEVICE_COUNT!_MAC=%%c
            echo !DEVICE_COUNT!. %%b (%%c)
        )
    )

    REM Clean up temp file
    del device_scan.tmp
)

REM Check if any devices were found
if %DEVICE_COUNT% EQU 0 (
    echo.
    echo ============================================================================
    echo                           NO DEVICES FOUND
    echo ============================================================================
    echo No compatible devices were found during scan.
    echo.
    echo Troubleshooting tips:
    echo   - Make sure device is powered on and advertising
    echo   - Check device name starts with: MR, CT, COP, or contains ESP32
    echo   - Move closer to the device
    echo   - Ensure device is not connected to other apps
    echo.
    echo You can also enter a MAC address manually.
    echo.
    set /p manual_choice="Enter MAC address manually? (Y/N): "
    if /i "!manual_choice!"=="Y" goto manual_mac
    pause
    exit /b 1
)

REM Let user select device
echo.
set /p device_choice="Enter device number (1-%DEVICE_COUNT%): "

REM Validate device selection
if "%device_choice%"=="" (
    echo ERROR: No device selected
    pause
    exit /b 1
)

REM Check if choice is valid number
set /a test_num=%device_choice% 2>nul
if %test_num% LSS 1 (
    echo ERROR: Invalid device number
    pause
    exit /b 1
)
if %test_num% GTR %DEVICE_COUNT% (
    echo ERROR: Device number out of range
    pause
    exit /b 1
)

REM Get selected device info
call set TARGET_MAC=%%DEVICE_%device_choice%_MAC%%
call set TARGET_NAME=%%DEVICE_%device_choice%_NAME%%

echo.
echo ============================================================================
echo Device selected: %TARGET_NAME% (%TARGET_MAC%)
echo ============================================================================

goto firmware_selection

:manual_mac
echo.
echo ============================================================================
echo                           MANUAL MAC ADDRESS ENTRY
echo ============================================================================
set /p TARGET_MAC="Enter MAC address (AA:BB:CC:DD:EE:FF): "
set TARGET_NAME=Manual Entry

REM Validate MAC address format (basic check)
if "%TARGET_MAC%"=="" (
    echo ERROR: No MAC address provided
    pause
    exit /b 1
)

echo "%TARGET_MAC%" | findstr /R "^[0-9A-Fa-f][0-9A-Fa-f]:[0-9A-Fa-f][0-9A-Fa-f]:[0-9A-Fa-f][0-9A-Fa-f]:[0-9A-Fa-f][0-9A-Fa-f]:[0-9A-Fa-f][0-9A-Fa-f]:[0-9A-Fa-f][0-9A-Fa-f]$" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Invalid MAC address format
    echo Expected format: AA:BB:CC:DD:EE:FF
    pause
    exit /b 1
)

echo.
echo ============================================================================
echo Device entered: %TARGET_NAME% (%TARGET_MAC%)
echo ============================================================================

:firmware_selection
echo.
echo ============================================================================
echo                           STEP 2: FIRMWARE SELECTION
echo ============================================================================

REM Scan for all firmware files in build directory
set FW_COUNT=0
echo Available firmware files:
echo.

for %%f in ("%BUILD_DIR%\*.bin") do (
    if exist "%%f" (
        set /a FW_COUNT+=1
        set FW_!FW_COUNT!_FILE=%%f
        set FW_!FW_COUNT!_NAME=%%~nxf

        REM Get file size
        set SIZE=%%~zf
        set /a SIZE_KB=!SIZE!/1024
        set /a SIZE_MB=!SIZE_KB!/1024

        REM Extract version from filename if possible
        set VERSION=%%~nxf
        set VERSION=!VERSION:Vantage_Bnry_nxESP32_FW_Int_Relse_=!
        set VERSION=!VERSION:Vantage_nxESP32_Int_Rel_=!
        set VERSION=!VERSION:.bin=!
        set VERSION=!VERSION:_=.!

        if !SIZE_MB! GTR 0 (
            echo !FW_COUNT!. %%~nxf (!SIZE_MB! MB^) - Version: !VERSION!
        ) else (
            echo !FW_COUNT!. %%~nxf (!SIZE_KB! KB^) - Version: !VERSION!
        )
    )
)

REM Check if any firmware files were found
if %FW_COUNT% EQU 0 (
    echo ERROR: No firmware files found in %BUILD_DIR%
    echo Please build the firmware first using: idf.py build
    pause
    exit /b 1
)

REM Let user select firmware
echo.
set /p fw_choice="Enter firmware number (1-%FW_COUNT%): "

REM Validate firmware selection
if "%fw_choice%"=="" (
    echo ERROR: No firmware selected
    pause
    exit /b 1
)

set /a test_fw_num=%fw_choice% 2>nul
if %test_fw_num% LSS 1 (
    echo ERROR: Invalid firmware number
    pause
    exit /b 1
)
if %test_fw_num% GTR %FW_COUNT% (
    echo ERROR: Firmware number out of range
    pause
    exit /b 1
)

REM Get selected firmware info
call set FIRMWARE_FILE=%%FW_%fw_choice%_FILE%%
call set FIRMWARE_NAME=%%FW_%fw_choice%_NAME%%

REM Extract version info for selected firmware
set VERSION_INFO=%FIRMWARE_NAME:Vantage_Bnry_nxESP32_FW_Int_Relse_=%
set VERSION_INFO=%VERSION_INFO:Vantage_nxESP32_Int_Rel_=%
set VERSION_INFO=%VERSION_INFO:.bin=%
set VERSION_INFO=%VERSION_INFO:_=.%

REM Get file size
for %%A in ("%FIRMWARE_FILE%") do set FIRMWARE_SIZE=%%~zA
set /a FIRMWARE_SIZE_KB=%FIRMWARE_SIZE%/1024

echo.
echo ============================================================================
echo Firmware selected: %FIRMWARE_NAME%
echo Version: %VERSION_INFO%
echo Size: %FIRMWARE_SIZE% bytes (%FIRMWARE_SIZE_KB% KB)
echo ============================================================================

REM Step 3: Confirmation
echo.
echo ============================================================================
echo                           STEP 3: CONFIRMATION
echo ============================================================================
echo Ready to start OTA firmware update:
echo.
echo Target Device: %TARGET_NAME% (%TARGET_MAC%)
echo Firmware File: %FIRMWARE_NAME%
echo Version: %VERSION_INFO%
echo Size: %FIRMWARE_SIZE_KB% KB
echo.
echo This will update the device firmware. The process may take several minutes.
echo ============================================================================
echo.

set /p final_confirm="Continue with firmware update? (Y/N): "
if /i not "%final_confirm%"=="Y" (
    echo.
    echo Firmware update cancelled by user.
    pause
    exit /b 0
)

:start_firmware_transfer
echo.
echo ============================================================================
echo                           STEP 4: FIRMWARE TRANSFER
echo ============================================================================
echo Target Device: %TARGET_NAME% (%TARGET_MAC%)
echo Firmware: %FIRMWARE_NAME%
echo Version: %VERSION_INFO%
echo Size: %FIRMWARE_SIZE_KB% KB
echo ============================================================================
echo.

REM Execute the Python OTA script with selected parameters
echo Starting firmware transfer to %TARGET_NAME% (%TARGET_MAC%)...
echo.

REM Run the Python script with the selected firmware and target device
python "%PYTHON_SCRIPT%" "%FIRMWARE_FILE%" --address "%TARGET_MAC%" --verbose

REM Capture the exit code immediately after Python script execution
set PYTHON_EXIT_CODE=%ERRORLEVEL%

echo.
echo ============================================================================

REM Check exit code and show appropriate message based on actual result
if %PYTHON_EXIT_CODE% EQU 0 (
    echo                    FIRMWARE FLASH COMPLETED SUCCESSFULLY!
    echo ============================================================================
    echo.
    echo ✅ SUCCESS: Device %TARGET_NAME% (%TARGET_MAC%) has been updated successfully!
    echo.
    echo Device Details:
    echo   - Target: %TARGET_NAME% (%TARGET_MAC%)
    echo   - Firmware: %FIRMWARE_NAME%
    echo   - Version: %VERSION_INFO%
    echo   - Size: %FIRMWARE_SIZE_KB% KB
    echo.
    echo The device will restart automatically with the updated firmware.
    echo You can now close this window.
) else if %PYTHON_EXIT_CODE% EQU 2 (
    echo                      FIRMWARE UPDATE NOT NEEDED
    echo ============================================================================
    echo.
    echo ℹ️  INFO: The device already has the same firmware version installed.
    echo.
    echo Device Details:
    echo   - Target: %TARGET_NAME% (%TARGET_MAC%)
    echo   - Current Version: %VERSION_INFO%
    echo.
    echo No update is required. The device is already up to date.
) else (
    echo                        FIRMWARE FLASH FAILED!
    echo ============================================================================
    echo.
    echo ❌ ERROR: Firmware update failed with exit code: %PYTHON_EXIT_CODE%
    echo.
    echo Device Details:
    echo   - Target: %TARGET_NAME% (%TARGET_MAC%)
    echo   - Firmware: %FIRMWARE_NAME%
    echo   - Version: %VERSION_INFO%
    echo.
    echo Please check the error messages above and try again.
    echo.
    echo Common troubleshooting tips:
    echo   - Make sure device is powered on and advertising
    echo   - Verify the device supports OTA updates
    echo   - Check that device has the correct OTA service UUID
    echo   - Move closer to the device to improve signal strength
    echo   - Ensure no other apps are connected to the device
    echo   - Try scanning again: python %PYTHON_SCRIPT% --scan
    echo.
    echo If the problem persists, the device may not support OTA updates
    echo or may require a different update method.
)

echo.
pause
