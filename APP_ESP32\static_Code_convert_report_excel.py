import pdfplumber
import pandas as pd
import re
from pathlib import Path
from datetime import datetime

# Excel template columns
columns = [
    "Sr. No.", "File Name", "Description", "Line No.", "Issue ID",
    "Checker Name", "Action", "Checker Description", "Priority",
    "Folder", "Issue", "Scope", "Comments"
]

def extract_pdf_text(pdf_path):
    """Extract text with multiple methods for maximum coverage"""
    all_text = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            # Try normal extraction first
            text = page.extract_text(layout=True)
            if not text or len(text.strip()) < 50:  # If too short
                # Try more aggressive extraction
                text = page.extract_text(
                    x_tolerance=3,
                    y_tolerance=3,
                    keep_blank_chars=True
                )
            if text:
                all_text.append(text)
    return "\n".join(all_text)

def group_issue_blocks(lines):
    """Group blocks with multiple separator patterns"""
    blocks = []
    current_block = []
    separators = [
        re.compile(r'^-{3,}$'),  # Dashes
        re.compile(r'^\*{3,}$'),  # Asterisks
        re.compile(r'^={3,}$'),  # Equals
        re.compile(r'^Report generated'),  # Metadata lines
    ]
    
    for line in lines:
        line = line.strip()
        if any(sep.match(line) for sep in separators):
            if current_block:
                blocks.append(current_block)
                current_block = []
        else:
            if line:
                current_block.append(line)
    
    if current_block:
        blocks.append(current_block)
    
    return blocks

def parse_issue_block(block, sr_no):
    """Ultra-flexible parsing with multiple fallback patterns"""
    if not block:
        return None

    # Master pattern with all possible variations
    patterns = [
        # Standard pattern
        re.compile(
            r'(?P<file>.+?):(?P<line>\d+)\s+'
            r'(?P<checker>[A-Z0-9_.-]+)\s*'
            r'\((?P<id>\d+):(?P<priority>Error|Critical|Info|Warning|Review|High|Medium|Low)\)',
            re.IGNORECASE
        ),
        # Alternative pattern 1
        re.compile(
            r'(?P<file>.+?):(?P<line>\d+)\s+'
            r'(?P<checker>[A-Z0-9_.-]+)\s*'
            r'\((?P<id>\d+)\)\s*'
            r'(?P<priority>Error|Critical|Info|Warning|Review|High|Medium|Low)',
            re.IGNORECASE
        ),
        # Alternative pattern 2 (for your specific unmatched case)
        re.compile(
            r'^\d+\s+\(System:\s*\d+\)\s+'
            r'(?P<file>.+?):(?P<line>\d+)\s+'
            r'(?P<checker>[A-Z0-9_.-]+)\s+'
            r'\((?P<id>\d+):(?P<priority>\w+)\)',
            re.IGNORECASE
        )
    ]

    for line in block:
        for pattern in patterns:
            match = pattern.search(line)
            if match:
                try:
                    file_path = match.group('file').strip()
                    line_no = match.group('line').strip()
                    checker_name = match.group('checker').strip()
                    issue_id = match.group('id').strip()
                    priority = match.group('priority').capitalize()

                    # Get description (all lines before match)
                    header_idx = block.index(line)
                    description = ' '.join(block[:header_idx]).strip()
                    description = re.sub(r'\s+', ' ', description)

                    # Format output
                    folder = Path(file_path).parts[0] if file_path else ""
                    file_path_quoted = f'"""{file_path}"""'

                    return [
                        sr_no, file_path_quoted, description, line_no, issue_id,
                        checker_name, "Analyze", "Static Analysis Finding", priority,
                        folder, "", "", ""
                    ]
                except Exception as e:
                    print(f"Error parsing matched line: {e}")
                    continue
    
    # Final fallback - try to extract minimal info
    if len(block) >= 2:
        last_line = block[-1]
        if ':' in last_line and any(char.isdigit() for char in last_line):
            parts = last_line.split()
            if len(parts) >= 3:
                try:
                    # Fallback minimal format
                    file_line = parts[0]
                    if ':' in file_line:
                        file, line = file_line.split(':')[:2]
                        checker = parts[1] if len(parts) > 1 else "UNKNOWN"
                        issue_id = "0"
                        priority = "Info"
                        
                        return [
                            sr_no, f'"""{file}"""', ' '.join(block[:-1]), line, issue_id,
                            checker, "Analyze", "Fallback Parsing", priority,
                            Path(file).parts[0] if file else "", "", "", ""
                        ]
                except:
                    pass
    
    return None

def main(pdf_path):
    print("\n" + "="*50)
    print("Starting Ultimate PDF Parser")
    print("="*50)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_excel = f"static_analysis_report_{timestamp}.xlsx"
    manual_review_file = f"manual_review_{timestamp}.txt"
    
    # Extract text
    pdf_text = extract_pdf_text(pdf_path)
    lines = [line.strip() for line in pdf_text.splitlines() if line.strip()]
    print(f"\nExtracted {len(lines)} non-empty lines from PDF")
    
    # Group blocks
    blocks = group_issue_blocks(lines)
    print(f"Grouped into {len(blocks)} potential issue blocks")
    
    # Parse blocks
    parsed_data = []
    unmatched_blocks = []
    sr_no = 1
    
    print("\nParsing issue blocks...")
    for i, block in enumerate(blocks):
        row = parse_issue_block(block, sr_no)
        if row:
            parsed_data.append(row)
            sr_no += 1
        else:
            unmatched_blocks.append(block)
            if i < 10:  # Show first 10 unmatched
                print(f"Unmatched block {i+1}:")
                print("\n".join(block[:3]) + "...")
                print("------")
    
    print(f"\nSuccessfully parsed {len(parsed_data)} issues")
    print(f"Found {len(unmatched_blocks)} unmatched blocks")
    
    # Save unmatched for manual review
    if unmatched_blocks:
        with open(manual_review_file, "w", encoding="utf-8") as f:
            for block in unmatched_blocks:
                f.write("\n".join(block) + "\n" + "="*80 + "\n")
        print(f"\nSaved {len(unmatched_blocks)} unmatched blocks to '{manual_review_file}'")
    
    # Final output
    df = pd.DataFrame(parsed_data, columns=columns)
    df.to_excel(output_excel, index=False)
    print(f"\nSaved {len(parsed_data)} issues to '{output_excel}'")
    
    # Summary
    print("\n" + "="*50)
    print("Final Report")
    print("="*50)
    print(f"Total blocks processed: {len(blocks)}")
    print(f"Successfully parsed: {len(parsed_data)}")
    print(f"Requires manual review: {len(unmatched_blocks)}")
    print("\nDone!")

if __name__ == "__main__":
    pdf_file = "20_KW_Vantage_BLE_Nexus_ESP32_Report.pdf"
    main(pdf_file)